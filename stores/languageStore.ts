import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import i18n from '@/lib/i18n';

export type Language = 'en' | 'ar';

interface LanguageState {
  language: Language;
  isRTL: boolean;
  setLanguage: (language: Language) => void;
  toggleLanguage: () => void;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set, get) => ({
      language: 'en',
      isRTL: false,
      
      setLanguage: (language: Language) => {
        const isRTL = language === 'ar';
        
        // Update i18n language
        i18n.changeLanguage(language);
        
        // Update document direction and lang attribute
        if (typeof document !== 'undefined') {
          document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
          document.documentElement.lang = language;
        }
        
        set({ language, isRTL });
      },
      
      toggleLanguage: () => {
        const currentLanguage = get().language;
        const newLanguage: Language = currentLanguage === 'en' ? 'ar' : 'en';
        get().setLanguage(newLanguage);
      },
    }),
    {
      name: 'language-storage',
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Ensure i18n is updated when store is rehydrated
          state.setLanguage(state.language);
        }
      },
    }
  )
);
