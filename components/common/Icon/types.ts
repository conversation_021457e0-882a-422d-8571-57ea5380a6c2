import { IconType } from 'react-icons'
import { 
  // Navigation & UI
  FiArrowRight,
  FiChevronRight,
  FiChevronDown,
  FiHome,
  FiUser,
  FiBriefcase,
  FiTrendingUp,
  FiFileText,
  FiMail,
  FiShield,
  FiSettings,
  FiLogOut,
  FiCreditCard,
  FiUsers,
  FiMessageSquare,
  FiPlus,
  FiUserPlus,
  FiGithub,
  FiLifeBuoy,
  FiCloud,
  FiCheck,
  FiCircle,
  FiGlobe,
  FiX,
  FiMenu,
  FiSun,
  
  FiMoon,
  FiArrowDown,
} from 'react-icons/fi'
import { BiMenuAltLeft } from 'react-icons/bi'
import { Vector } from './vector'
import {
  // Additional icons from other icon sets if needed
  HiOutlineArrowRight,
  HiOutlineChevronRight,
  HiOutlineChevronDown,
} from 'react-icons/hi2'

import {
  // Material Design icons for additional options
  MdArrowForward,
  MdKeyboardArrowRight,
  MdKeyboardArrowDown,
} from 'react-icons/md'

// Icon name mapping to actual icon components
export const iconMap = {
  // Navigation & UI
  'arrow-right': FiArrowRight,
  'chevron-right': FiChevronRight,
  'chevron-down': FiChevronDown,
  'home': FiHome,
  'user': FiUser,
  'briefcase': FiBriefcase,
  'trending-up': FiTrendingUp,
  'file-text': FiFileText,
  'mail': FiMail,
  'shield': FiShield,
  'settings': FiSettings,
  'log-out': FiLogOut,
  'credit-card': FiCreditCard,
  'users': FiUsers,
  'message-square': FiMessageSquare,
  'plus': FiPlus,
  'arrow-menu-alt-left': BiMenuAltLeft,
  'user-plus': FiUserPlus,
  'github': FiGithub,
  'life-buoy': FiLifeBuoy,
  'cloud': FiCloud,
  'check': FiCheck,
  'circle': FiCircle,
  'globe': FiGlobe,
  'x': FiX,
  'menu': FiMenu,
  'sun': FiSun,
  'moon': FiMoon,
  'arrow-down': FiArrowDown,
  
  // Alternative icons (if you want different styles)
  'arrow-right-outline': HiOutlineArrowRight,
  'chevron-right-outline': HiOutlineChevronRight,
  'chevron-down-outline': HiOutlineChevronDown,
  'arrow-forward': MdArrowForward,
  'keyboard-arrow-right': MdKeyboardArrowRight,
  'keyboard-arrow-down': MdKeyboardArrowDown,
  'vector': Vector,
} as const

// Type for all available icon names
export type IconName = keyof typeof iconMap

// Icon component props interface
export interface IconProps {
  name: IconName
  size?: number | string
  className?: string
  color?: string
  style?: React.CSSProperties
  onClick?: () => void
  'aria-label'?: string
}

// Export the IconType for external use
export type { IconType }
