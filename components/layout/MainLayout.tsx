"use client"

import { Head<PERSON> } from "./Header"
import { Footer } from "./Footer"

interface MainLayoutProps {
  children: React.ReactNode
  hasFullScreenBanner?: boolean
}

export function MainLayout({ children, hasFullScreenBanner = false }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-white-50 dark:bg-dark-secondary-600 transition-colors duration-300">
      <Header />

      {/* Main content area */}
      <main className="flex-1">
        {hasFullScreenBanner ? (
          // For pages with full-screen banner, don't add container padding
          children
        ) : (
          // For regular pages, use container with padding
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {children}
          </div>
        )}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
