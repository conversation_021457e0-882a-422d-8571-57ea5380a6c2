'use client';

import { useState } from 'react';
import { PromoBanner } from '@/components/homePage/PromoBanner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

type SpeedOption = 'slow' | 'normal' | 'fast' | 'very-fast';

export function SpeedControlDemo() {
  const [currentSpeed, setCurrentSpeed] = useState<SpeedOption>('normal');

  const speedOptions: { value: SpeedOption; label: string; duration: string }[] = [
    { value: 'slow', label: 'Slow', duration: '25s' },
    { value: 'normal', label: 'Normal', duration: '15s' },
    { value: 'fast', label: 'Fast', duration: '8s' },
    { value: 'very-fast', label: 'Very Fast', duration: '5s' },
  ];

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-center">Promotional Banner Speed Control</CardTitle>
        <p className="text-center text-muted-foreground">
          Test different animation speeds for the promotional banner
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Speed Control Buttons */}
        <div className="flex flex-wrap justify-center gap-2">
          {speedOptions.map((option) => (
            <Button
              key={option.value}
              variant={currentSpeed === option.value ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrentSpeed(option.value)}
              className="min-w-[100px]"
            >
              {option.label}
              <span className="ml-1 text-xs opacity-70">({option.duration})</span>
            </Button>
          ))}
        </div>

        {/* Current Speed Display */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Current Speed: <span className="font-medium">{speedOptions.find(s => s.value === currentSpeed)?.label}</span>
            {' '}({speedOptions.find(s => s.value === currentSpeed)?.duration})
          </p>
        </div>

        {/* Live Preview */}
        <div className="border rounded-lg overflow-hidden">
          <PromoBanner speed={currentSpeed} />
        </div>

        {/* Speed Descriptions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          {speedOptions.map((option) => (
            <div
              key={option.value}
              className={`p-3 rounded-lg border ${
                currentSpeed === option.value
                  ? 'bg-primary/10 border-primary'
                  : 'bg-muted/50 border-border'
              }`}
            >
              <h4 className="font-medium">{option.label}</h4>
              <p className="text-xs text-muted-foreground mt-1">
                {option.value === 'slow' && 'Relaxed reading pace'}
                {option.value === 'normal' && 'Balanced speed'}
                {option.value === 'fast' && 'Quick attention grabber'}
                {option.value === 'very-fast' && 'High energy ticker'}
              </p>
            </div>
          ))}
        </div>

        {/* Usage Instructions */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">How to Use:</h4>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>• Import: <code className="bg-background px-1 rounded">import {`{ PromoBanner }`} from '@/components/homePage/PromoBanner'</code></p>
            <p>• Usage: <code className="bg-background px-1 rounded">{`<PromoBanner speed="fast" />`}</code></p>
            <p>• Options: slow (25s), normal (15s), fast (8s), very-fast (5s)</p>
            <p>• Default: normal speed if no speed prop is provided</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
