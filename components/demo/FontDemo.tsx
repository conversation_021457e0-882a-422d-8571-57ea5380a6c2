'use client';

import { useTranslation } from 'react-i18next';
import { useLanguageStore } from '@/stores/languageStore';

export function FontDemo() {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguageStore();

  return (
    <div className="p-6 rounded-xl bg-gray-50 dark:bg-slate-800 border border-gray-200 dark:border-slate-700">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-slate-100 mb-4">
        Font Demo
      </h3>

      <div className="space-y-6">
        {/* Current Font Info */}
        <div className="flex items-center justify-between p-4 bg-white dark:bg-slate-700 rounded-lg">
          <span className="text-gray-700 dark:text-gray-300">Current Font:</span>
          <span className="font-medium text-blue-600 dark:text-blue-400">
            {language === 'en' ? 'Legquinne-Regular' : 'Sukar Black Font'}
          </span>
        </div>

        {/* Font Sample Text */}
        <div className="space-y-4">
          <div className="p-4 bg-white dark:bg-slate-700 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-slate-100 mb-2">
              Sample Text:
            </h4>
            <div className="space-y-2">
              <p className="text-lg text-gray-700 dark:text-gray-300">
                {language === 'en' 
                  ? 'This text is displayed using Legquinne-Regular font for English content.'
                  : 'هذا النص يُعرض باستخدام خط Sukar Black Font للمحتوى العربي.'
                }
              </p>
              <p className="text-base text-gray-600 dark:text-gray-400">
                {language === 'en'
                  ? 'The font automatically switches based on the selected language.'
                  : 'يتغير الخط تلقائياً بناءً على اللغة المختارة.'
                }
              </p>
            </div>
          </div>

          {/* Font Sizes Demo */}
          <div className="p-4 bg-white dark:bg-slate-700 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-slate-100 mb-2">
              Different Sizes:
            </h4>
            <div className="space-y-2">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {language === 'en' ? 'Extra Small Text (12px)' : 'نص صغير جداً (12px)'}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {language === 'en' ? 'Small Text (14px)' : 'نص صغير (14px)'}
              </p>
              <p className="text-base text-gray-700 dark:text-gray-300">
                {language === 'en' ? 'Base Text (16px)' : 'نص أساسي (16px)'}
              </p>
              <p className="text-lg text-gray-700 dark:text-gray-300">
                {language === 'en' ? 'Large Text (18px)' : 'نص كبير (18px)'}
              </p>
              <p className="text-xl text-gray-800 dark:text-gray-200">
                {language === 'en' ? 'Extra Large Text (20px)' : 'نص كبير جداً (20px)'}
              </p>
              <p className="text-2xl text-gray-900 dark:text-gray-100">
                {language === 'en' ? 'Heading Text (24px)' : 'نص عنوان (24px)'}
              </p>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {language === 'en'
              ? 'Switch the language using the globe icon in the header to see the font change.'
              : 'قم بتغيير اللغة باستخدام أيقونة الكرة الأرضية في الرأس لرؤية تغيير الخط.'
            }
          </p>
        </div>
      </div>
    </div>
  );
}
