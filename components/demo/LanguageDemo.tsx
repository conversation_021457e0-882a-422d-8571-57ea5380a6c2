'use client';

import { useTranslation } from 'react-i18next';
import { useLanguageStore } from '@/stores/languageStore';

export function LanguageDemo() {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguageStore();

  return (
    <div className="p-6 rounded-xl bg-gray-50 dark:bg-slate-800 border border-gray-200 dark:border-slate-700">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-slate-100 mb-4">
        {t('language.language')} Demo
      </h3>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-white dark:bg-slate-700 rounded-lg">
          <span className="text-gray-700 dark:text-gray-300">Current Language:</span>
          <span className="font-medium text-blue-600 dark:text-blue-400">
            {language === 'en' ? 'English' : 'العربية'}
          </span>
        </div>

        <div className="flex items-center justify-between p-4 bg-white dark:bg-slate-700 rounded-lg">
          <span className="text-gray-700 dark:text-gray-300">Text Direction:</span>
          <span className="font-medium text-blue-600 dark:text-blue-400">
            {isRTL ? 'RTL (Right to Left)' : 'LTR (Left to Right)'}
          </span>
        </div>

        <div className="p-4 bg-white dark:bg-slate-700 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-slate-100 mb-2">
            {t('demo.features')}:
          </h4>
          <ul className="space-y-2 text-gray-700 dark:text-gray-300">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></span>
              {t('demo.feature_1')}
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></span>
              {t('demo.feature_2')}
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></span>
              {t('demo.feature_3')}
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 rtl:mr-0 rtl:ml-3"></span>
              {t('demo.feature_4')}
            </li>
          </ul>
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Click the language toggle button (
            <span className="font-mono bg-gray-200 dark:bg-gray-600 px-1 rounded">
              {language === 'en' ? 'ع' : 'EN'}
            </span>
            ) in the header to switch languages
          </p>
        </div>
      </div>
    </div>
  );
}
