"use client"

import { useTranslation } from "react-i18next"
import { JewelryCard } from "@/components/common/JewelryCard"

export function AttractiveJewellery() {
  const { t } = useTranslation()

  const collections = [
    {
      key: "brooch",
      title: t("collections.brooch"),
      image: "/images/collections/brooch.png"
    },
    {
      key: "earrings",
      title: t("collections.earrings"),
      image: "/images/collections/earrings.png"
    },
    {
      key: "necklaces",
      title: t("collections.necklaces"),
      image: "/images/collections/necklaces.png"
    },
    {
      key: "bracelet",
      title: t("collections.bracelet"),
      image: "/images/collections/bracelet.png"
    },
    {
      key: "ring",
      title: t("collections.ring"),
      image: "/images/collections/ring.png"
    }
  ]

  const handleCollectionClick = (collectionKey: string) => {
    console.log(`Clicked on ${collectionKey} collection`)
    // Add navigation logic here
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          {/* Section Title */}
          <p className="text-primary-500 text-lg uppercase tracking-wider mb-2">
            {t("collections.section_title")}
          </p>

          {/* Main Title */}
          <h2 className="text-3xl md:text-6xl font-bold text-secondary-500 dark:text-white-500">
            {t("collections.title")}
          </h2>
        </div>

        {/* Collections Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 md:gap-8">
          {collections.map((collection) => (
            <JewelryCard
              key={collection.key}
              title={collection.title}
              image={collection.image}
              onClick={() => handleCollectionClick(collection.key)}
              className="w-full"
            />
          ))}
        </div>
      </div>
    </section>
  )
}
