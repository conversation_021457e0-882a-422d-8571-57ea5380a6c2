"use client"

import { useTranslation } from "react-i18next"
import { useLanguageStore } from "@/stores/languageStore"
import { Icon } from "@/components/common/Icon"

interface PromoBannerProps {
  speed?: "slow" | "normal" | "fast" | "very-fast"
}

export function PromoBanner({ speed = "normal" }: PromoBannerProps) {
  const { t } = useTranslation()
  const { isRTL } = useLanguageStore()

  return (
    <section className="relative w-full overflow-hidden bg-secondary-500 dark:bg-secondary-600 dark:border dark:border-secondary-700 text-custom-borderLight ">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_20%_50%,rgba(255,255,255,0.3),transparent_50%)]" />
        <div className="absolute top-0 right-0 w-full h-full bg-[radial-gradient(circle_at_80%_50%,rgba(255,255,255,0.2),transparent_50%)]" />
      </div>

      {/* Decorative Elements */}

      {/* Marquee Container */}
      <div
        className={`marquee-container relative py-4 h-16 flex items-center marquee-${speed}`}
      >
        <div className="flex items-center whitespace-nowrap">
          {/* Multiple copies of the text for seamless loop */}
          <div
            className={`inline-flex items-center ${
              isRTL ? "marquee-rtl" : "marquee"
            }`}
          >
            {/* Repeat the content multiple times for seamless infinite loop */}
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="inline-flex items-center">
                <span className="text-custom-borderLight text-lg md:text-xl font-medium px-6">
                  {t("promo.text")}
                </span>

                {/* Diamond Icon */}
                <div className="px-4">
                  <Icon
                    name="vector"
                    size={8}
                    className="text-primary-300 opacity-80"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-black/10 to-transparent"></div>
    </section>
  )
}
