"use client"

import { useTranslation } from "react-i18next"
import { useLanguageStore } from "@/stores/languageStore"

export function PromoBanner() {
  const { t } = useTranslation()
  const { isRTL } = useLanguageStore()

  return (
    <section className="relative w-full overflow-hidden bg-secondary-500 dark:bg-secondary-600 dark:border dark:border-secondary-700 text-custom-borderLight ">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_20%_50%,rgba(255,255,255,0.3),transparent_50%)]" />
        <div className="absolute top-0 right-0 w-full h-full bg-[radial-gradient(circle_at_80%_50%,rgba(255,255,255,0.2),transparent_50%)]" />
      </div>

      {/* Decorative Elements */}

      {/* Marquee Container */}
      <div className="marquee-container relative py-4 h-16 flex items-center">
        <div className="flex items-center whitespace-nowrap">
          {/* Multiple copies of the text for seamless loop */}
          <div
            className={`inline-flex items-center ${
              isRTL ? "marquee-rtl" : "marquee"
            }`}
          >
            <span className="text-custom-borderLight text-lg md:text-xl font-medium px-8">
              {t("promo.text")}
            </span>
            <span className="text-custom-borderLight/80 text-lg md:text-xl px-4">•</span>
            <span className="text-custom-borderLight text-lg md:text-xl font-medium px-8">
              {t("promo.text")}
            </span>
            <span className="text-custom-borderLight/80 text-lg md:text-xl px-4">•</span>
            <span className="text-custom-borderLight text-lg md:text-xl font-medium px-8">
              {t("promo.text")}
            </span>
            <span className="text-custom-borderLight/80 text-lg md:text-xl px-4">•</span>
            <span className="text-custom-borderLight text-lg md:text-xl font-medium px-8">
              {t("promo.text")}
            </span>
            <span className="text-custom-borderLight/80 text-lg md:text-xl px-4">•</span>
          </div>
        </div>
      </div>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-black/10 to-transparent"></div>
    </section>
  )
}
