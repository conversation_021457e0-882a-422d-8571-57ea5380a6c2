'use client';
import { ThemeInitializer } from '@/components/theme/ThemeInitializer';
import { LanguageInitializer } from '@/components/language/LanguageInitializer';
import './globals.css';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" dir="ltr">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>
        <ThemeInitializer />
        <LanguageInitializer />
        {children}
      </body>
    </html>
  );
}