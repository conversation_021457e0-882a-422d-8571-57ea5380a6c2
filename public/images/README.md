# Image Assets

This directory contains image assets for the website.

## Required Files

### Banner Images
- **jewelry-banner-poster.jpg** - Poster image for the banner video (shown while video loads)

### Logo Images
- **logo-light.png** - Logo for light theme (dark logo on transparent background)
- **logo-dark.png** - Logo for dark theme (light logo on transparent background)
- **logo-light.svg** - SVG version of light theme logo (preferred for scalability)
- **logo-dark.svg** - SVG version of dark theme logo (preferred for scalability)

## Image Specifications

### Banner Poster Requirements:
- **Resolution**: 1920x1080 (Full HD)
- **Aspect Ratio**: 16:9
- **Format**: JPG or PNG
- **Content**: Representative frame from the banner video or elegant jewelry image
- **Style**: Professional, high-quality, matches the video content

### Logo Requirements:
- **Format**: PNG or SVG (SVG preferred for scalability)
- **Size**: Minimum 200x60px, scalable
- **Background**: Transparent
- **Light Theme Logo**: Dark colored logo for light backgrounds
- **Dark Theme Logo**: Light colored logo for dark backgrounds
- **Style**: Professional, clean, readable at small sizes
- **Brand**: Should represent jewelry/luxury brand identity

## Placeholder

Until the actual image files are added, the banner component will show:
1. A golden gradient background
2. Animated patterns

## Adding Images

1. Place your image files in this directory
2. Ensure they match the naming convention:
   - `jewelry-banner-poster.jpg`
3. The banner component will automatically use them

## Optimization Tips

- Optimize images for web (use tools like ImageOptim, TinyPNG)
- Keep file sizes reasonable (under 500KB for poster images)
- Use appropriate formats (JPG for photos, PNG for graphics with transparency)
- Consider creating responsive versions for different screen sizes
